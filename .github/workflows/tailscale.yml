name: Sync Tailscale ACLs

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

permissions:
  # Give the default GITHUB_TOKEN write permission to commit and push the changed files back to the repository.
  contents: write

jobs:
  acls:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}

      - name: Deploy ACL
        if: github.event_name == 'push'
        id: deploy-acl
        uses: tailscale/gitops-acl-action@v1
        with:
          api-key: ${{ secrets.TS_API_KEY }}
          tailnet: ${{ secrets.TS_TAILNET }}
          action: apply

      - name: Test ACL
        if: github.event_name == 'pull_request'
        id: test-acl
        uses: tailscale/gitops-acl-action@v1
        with:
          api-key: ${{ secrets.TS_API_KEY }}
          tailnet: ${{ secrets.TS_TAILNET }}
          action: test

      # Rebuild HTML
      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: "pip" # caching pip dependencies

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Run Python script to generate network map
        run: python main.py

      # This will place the newly generated network map HTML file back into the repo
      - name: Update network map in repo
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: Auto updating network_topology.html
      # Be aware of limitations here: https://github.com/stefanzweifel/git-auto-commit-action?tab=readme-ov-file#limitations--gotchas
