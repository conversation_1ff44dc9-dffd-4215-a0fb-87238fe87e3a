name: Sync Tailscale ACL & Generate Network Topology

# Workflow for managing Tailscale ACLs and generating network topology visualizations
# Supports both ACL validation/deployment and automated network map generation with proper security controls

on:
  push:
    branches: ["main"]
    paths:
      - "policy.hujson"
      - "*.py"
      - "requirements.txt"
      - ".github/workflows/tailscale.yml"
  pull_request:
    branches: ["main"]
    paths:
      - "policy.hujson"
      - "*.py"
      - "requirements.txt"
      - ".github/workflows/tailscale.yml"
  workflow_dispatch:
    inputs:
      force_regenerate:
        description: "Force regenerate network topology even if no changes detected"
        required: false
        default: false
        type: boolean
      skip_acl_operations:
        description: "Skip ACL operations (useful for topology-only updates)"
        required: false
        default: false
        type: boolean

# Global security settings
permissions:
  contents: read  # Default to read-only, escalate per job as needed

# Prevent concurrent runs that could conflict with each other
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PYTHON_VERSION: "3.13"
  CACHE_VERSION: "v1"  # Increment to invalidate caches

jobs:
  # Validate environment and dependencies before proceeding
  validate-environment:
    name: "Validate Environment and Dependencies"
    runs-on: ubuntu-latest
    timeout-minutes: 5
    outputs:
      python-cache-key: ${{ steps.cache-keys.outputs.python-cache-key }}
      requirements-hash: ${{ steps.cache-keys.outputs.requirements-hash }}
      should-run-acl: ${{ steps.changes.outputs.should-run-acl }}
      should-run-topology: ${{ steps.changes.outputs.should-run-topology }}

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
        with:
          fetch-depth: 2  # Need previous commit for change detection

      - name: "Validate required secrets"
        run: |
          if [[ "${{ github.event_name }}" == "push" || "${{ github.event_name }}" == "pull_request" ]]; then
            if [[ -z "${{ secrets.TS_API_KEY }}" ]]; then
              echo "ERROR: TS_API_KEY secret is required but not set"
              exit 1
            fi
            if [[ -z "${{ secrets.TS_TAILNET }}" ]]; then
              echo "ERROR: TS_TAILNET secret is required but not set"
              exit 1
            fi
            echo "SUCCESS: Required secrets are configured"
          fi

      - name: "Detect changes and determine job execution"
        id: changes
        run: |
          # Determine what should run based on changes and inputs
          should_run_acl="false"
          should_run_topology="false"

          if [[ "${{ github.event.inputs.skip_acl_operations }}" == "true" ]]; then
            echo "INFO: Skipping ACL operations per user input"
          elif [[ "${{ github.event_name }}" == "push" || "${{ github.event_name }}" == "pull_request" ]]; then
            if git diff --name-only HEAD~1 HEAD | grep -E "(policy\.hujson|\.github/workflows/tailscale\.yml)" > /dev/null; then
              should_run_acl="true"
              echo "INFO: ACL-related files changed, will run ACL operations"
            fi
          fi

          if [[ "${{ github.event.inputs.force_regenerate }}" == "true" ]]; then
            should_run_topology="true"
            echo "INFO: Force regenerate requested, will run topology generation"
          elif [[ "${{ github.event_name }}" == "push" ]] || [[ "$should_run_acl" == "true" ]]; then
            if git diff --name-only HEAD~1 HEAD | grep -E "(policy\.hujson|.*\.py|requirements\.txt)" > /dev/null; then
              should_run_topology="true"
              echo "INFO: Topology-related files changed, will run topology generation"
            fi
          fi

          echo "should-run-acl=$should_run_acl" >> $GITHUB_OUTPUT
          echo "should-run-topology=$should_run_topology" >> $GITHUB_OUTPUT

      - name: "Generate cache keys"
        id: cache-keys
        run: |
          requirements_hash=$(sha256sum requirements.txt | cut -d' ' -f1)
          python_cache_key="${{ env.CACHE_VERSION }}-python-${{ env.PYTHON_VERSION }}-$requirements_hash"

          echo "python-cache-key=$python_cache_key" >> $GITHUB_OUTPUT
          echo "requirements-hash=$requirements_hash" >> $GITHUB_OUTPUT
          echo "INFO: Cache key: $python_cache_key"

  # Run comprehensive tests before any operations
  test-suite:
    name: "Run Test Suite"
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: validate-environment
    if: needs.validate-environment.outputs.should-run-acl == 'true' || needs.validate-environment.outputs.should-run-topology == 'true'

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2

      - name: "Set up Python ${{ env.PYTHON_VERSION }}"
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065  # v5.6.0
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
          cache-dependency-path: 'requirements.txt'

      - name: "Install dependencies"
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov  # Add testing dependencies

      - name: "Run tests with coverage"
        run: |
          cd tests
          python -m pytest --cov=../ --cov-report=term-missing --cov-report=xml

      - name: "Upload coverage reports"
        if: always()
        continue-on-error: true
        run: |
          echo "INFO: Test coverage completed"
          # Could integrate with coverage services here

  # ACL Management Job - Handles Tailscale ACL operations
  acl-management:
    name: "Tailscale ACL Management"
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [validate-environment, test-suite]
    if: needs.validate-environment.outputs.should-run-acl == 'true'

    permissions:
      contents: read
      id-token: write  # For potential OIDC authentication

    environment:
      name: ${{ github.event_name == 'push' && 'production' || 'staging' }}

    outputs:
      acl-operation-status: ${{ steps.acl-result.outputs.status }}
      acl-operation-details: ${{ steps.acl-result.outputs.details }}

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
        with:
          ref: ${{ github.head_ref || github.ref }}

      - name: "Enhanced ACL policy validation"
        run: |
          if [[ ! -f "policy.hujson" ]]; then
            echo "ERROR: policy.hujson file not found"
            exit 1
          fi

          # Comprehensive policy validation using Python
          python3 -c "
          import hjson
          import sys
          import re
          import ipaddress

          def validate_ip_or_cidr(ip_str):
              '''Validate IP address or CIDR range'''
              try:
                  if '/' in ip_str:
                      ipaddress.ip_network(ip_str, strict=False)
                  else:
                      ipaddress.ip_address(ip_str)
                  return True
              except ValueError:
                  return False

          def validate_port(port_str):
              '''Validate port number or range'''
              if port_str == '*':
                  return True
              if '-' in port_str:
                  try:
                      start, end = port_str.split('-')
                      return 1 <= int(start) <= int(end) <= 65535
                  except ValueError:
                      return False
              if ',' in port_str:
                  try:
                      ports = port_str.split(',')
                      return all(1 <= int(p.strip()) <= 65535 for p in ports)
                  except ValueError:
                      return False
              try:
                  return 1 <= int(port_str) <= 65535
              except ValueError:
                  return False

          def validate_autogroup(autogroup):
              '''Validate autogroup syntax'''
              valid_autogroups = [
                  'autogroup:internet', 'autogroup:self', 'autogroup:owner', 'autogroup:admin',
                  'autogroup:member', 'autogroup:tagged', 'autogroup:auditor', 'autogroup:billing-admin',
                  'autogroup:it-admin', 'autogroup:network-admin', 'autogroup:shared', 'autogroup:nonroot',
                  'autogroup:danger-all'
              ]
              if autogroup in valid_autogroups:
                  return True
              # Check domain-based autogroups
              if autogroup.startswith('user:*@') or autogroup.startswith('localpart:*@'):
                  return True
              return False

          try:
              print('🔍 Starting comprehensive ACL policy validation...')

              with open('policy.hujson') as f:
                  policy = hjson.load(f)

              print('✅ Valid HJSON syntax')

              # Validate top-level structure
              valid_sections = [
                  'acls', 'grants', 'groups', 'tagOwners', 'hosts', 'ssh', 'tests',
                  'sshTests', 'autoApprovers', 'nodeAttrs', 'postures', 'ipsets',
                  'derpMap', 'disableIPv4', 'OneCGNATRoute', 'randomizeClientPort'
              ]

              warnings = []
              errors = []

              for key in policy.keys():
                  if key not in valid_sections:
                      warnings.append(f'Unknown top-level section: {key}')

              # Collect all defined tags and groups for reference validation
              defined_tags = set()
              defined_groups = set()
              defined_hosts = set()

              # Validate tagOwners section
              if 'tagOwners' in policy:
                  print('🏷️  Validating tagOwners section...')
                  for tag, owners in policy['tagOwners'].items():
                      if not tag.startswith('tag:'):
                          errors.append(f'Tag \"{tag}\" must start with \"tag:\" prefix')
                      else:
                          defined_tags.add(tag)

                      if not isinstance(owners, list):
                          errors.append(f'Tag owners for \"{tag}\" must be a list')
                          continue

                      for owner in owners:
                          if isinstance(owner, str):
                              if owner.startswith('group:') and owner not in defined_groups:
                                  # We'll validate this after processing groups
                                  pass
                              elif owner.startswith('autogroup:') and not validate_autogroup(owner):
                                  errors.append(f'Invalid autogroup \"{owner}\" in tag \"{tag}\"')

              # Validate groups section
              if 'groups' in policy:
                  print('👥 Validating groups section...')
                  for group, members in policy['groups'].items():
                      if not group.startswith('group:'):
                          errors.append(f'Group \"{group}\" must start with \"group:\" prefix')
                      else:
                          defined_groups.add(group)

                      if not isinstance(members, list):
                          errors.append(f'Group members for \"{group}\" must be a list')
                          continue

                      for member in members:
                          if not isinstance(member, str):
                              errors.append(f'Group member in \"{group}\" must be a string')
                          elif '@' not in member and not member.startswith('autogroup:'):
                              warnings.append(f'Group member \"{member}\" in \"{group}\" may be invalid (no @ symbol)')

              # Validate hosts section
              if 'hosts' in policy:
                  print('🖥️  Validating hosts section...')
                  for host, address in policy['hosts'].items():
                      if '@' in host:
                          errors.append(f'Host name \"{host}\" cannot contain @ character')
                      defined_hosts.add(host)

                      if not validate_ip_or_cidr(address):
                          errors.append(f'Invalid IP address or CIDR \"{address}\" for host \"{host}\"')

              # Validate ACLs section
              if 'acls' in policy:
                  print('🔒 Validating ACLs section...')
                  for i, rule in enumerate(policy['acls']):
                      if not isinstance(rule, dict):
                          errors.append(f'ACL rule {i} must be an object')
                          continue

                      # Check required fields
                      if 'src' not in rule:
                          errors.append(f'ACL rule {i} missing required \"src\" field')
                      if 'dst' not in rule:
                          errors.append(f'ACL rule {i} missing required \"dst\" field')

                      # Validate action
                      if 'action' in rule and rule['action'] != 'accept':
                          errors.append(f'ACL rule {i} has invalid action \"{rule[\"action\"]}\" (only \"accept\" is allowed)')

                      # Validate sources
                      if 'src' in rule:
                          for src in rule['src']:
                              if src.startswith('tag:') and src not in defined_tags:
                                  errors.append(f'ACL rule {i} references undefined tag \"{src}\"')
                              elif src.startswith('group:') and src not in defined_groups:
                                  errors.append(f'ACL rule {i} references undefined group \"{src}\"')
                              elif src.startswith('autogroup:') and not validate_autogroup(src):
                                  errors.append(f'ACL rule {i} has invalid autogroup \"{src}\"')

                      # Validate destinations
                      if 'dst' in rule:
                          for dst in rule['dst']:
                              if ':' in dst:
                                  host_part, port_part = dst.rsplit(':', 1)
                                  if not validate_port(port_part):
                                      errors.append(f'ACL rule {i} has invalid port \"{port_part}\" in destination \"{dst}\"')

                                  if host_part.startswith('tag:') and host_part not in defined_tags:
                                      errors.append(f'ACL rule {i} references undefined tag \"{host_part}\"')
                                  elif host_part.startswith('group:') and host_part not in defined_groups:
                                      errors.append(f'ACL rule {i} references undefined group \"{host_part}\"')
                                  elif host_part in defined_hosts:
                                      pass  # Valid host reference
                                  elif validate_ip_or_cidr(host_part):
                                      pass  # Valid IP/CIDR
                                  elif host_part.startswith('autogroup:') and not validate_autogroup(host_part):
                                      errors.append(f'ACL rule {i} has invalid autogroup \"{host_part}\"')

              # Validate grants section (similar to ACLs but with different structure)
              if 'grants' in policy:
                  print('🎯 Validating grants section...')
                  for i, grant in enumerate(policy['grants']):
                      if not isinstance(grant, dict):
                          errors.append(f'Grant rule {i} must be an object')
                          continue

                      # Check required fields for grants
                      if 'src' not in grant:
                          errors.append(f'Grant rule {i} missing required \"src\" field')
                      if 'dst' not in grant:
                          errors.append(f'Grant rule {i} missing required \"dst\" field')

                      # Validate sources and destinations similar to ACLs
                      for field in ['src', 'dst']:
                          if field in grant:
                              for item in grant[field]:
                                  if item.startswith('tag:') and item not in defined_tags:
                                      errors.append(f'Grant rule {i} references undefined tag \"{item}\" in {field}')
                                  elif item.startswith('group:') and item not in defined_groups:
                                      errors.append(f'Grant rule {i} references undefined group \"{item}\" in {field}')
                                  elif item.startswith('autogroup:') and not validate_autogroup(item):
                                      errors.append(f'Grant rule {i} has invalid autogroup \"{item}\" in {field}')

              # Validate tests section
              if 'tests' in policy:
                  print('🧪 Validating tests section...')
                  for i, test in enumerate(policy['tests']):
                      if 'src' not in test:
                          errors.append(f'Test {i} missing required \"src\" field')
                      if 'accept' not in test and 'deny' not in test:
                          errors.append(f'Test {i} must have either \"accept\" or \"deny\" field')

              # Print results
              if warnings:
                  print('⚠️  Warnings:')
                  for warning in warnings:
                      print(f'   • {warning}')

              if errors:
                  print('❌ Validation errors found:')
                  for error in errors:
                      print(f'   • {error}')
                  print(f'\\n💡 Please fix {len(errors)} error(s) before proceeding.')
                  sys.exit(1)
              else:
                  print(f'✅ Enhanced ACL policy validation passed!')
                  if warnings:
                      print(f'   Found {len(warnings)} warning(s) but no blocking errors.')
                  else:
                      print('   No errors or warnings found.')

          except hjson.HjsonDecodeError as e:
              print(f'❌ HJSON syntax error: {e}')
              sys.exit(1)
          except Exception as e:
              print(f'❌ Validation failed: {e}')
              sys.exit(1)
          "

      # OAuth client authentication provides better security than API keys:
      # - More granular permissions and scoping
      # - Automatic token rotation and expiration
      # - Better audit trails and access control
      # - Reduced risk of credential exposure
      # To migrate: Create OAuth client in Tailscale admin console and set TS_OAUTH_CLIENT_ID and TS_OAUTH_SECRET

      - name: "Deploy ACL to Production"
        if: github.event_name == 'push'
        id: deploy-acl
        uses: tailscale/gitops-acl-action@v1.3.1
        with:
          # Use OAuth client authentication if available, fallback to API key
          api-key: ${{ secrets.TS_OAUTH_CLIENT_ID && '' || secrets.TS_API_KEY }}
          oauth-client-id: ${{ secrets.TS_OAUTH_CLIENT_ID }}
          oauth-secret: ${{ secrets.TS_OAUTH_SECRET }}
          tailnet: ${{ secrets.TS_TAILNET }}
          action: apply
          policy-file: policy.hujson
        continue-on-error: true

      - name: "Test ACL Changes"
        if: github.event_name == 'pull_request'
        id: test-acl
        uses: tailscale/gitops-acl-action@v1.3.1
        with:
          # Use OAuth client authentication if available, fallback to API key
          api-key: ${{ secrets.TS_OAUTH_CLIENT_ID && '' || secrets.TS_API_KEY }}
          oauth-client-id: ${{ secrets.TS_OAUTH_CLIENT_ID }}
          oauth-secret: ${{ secrets.TS_OAUTH_SECRET }}
          tailnet: ${{ secrets.TS_TAILNET }}
          action: test
          policy-file: policy.hujson
        continue-on-error: true

      - name: "Process ACL operation results"
        id: acl-result
        if: always() && (steps.deploy-acl.outcome != 'skipped' || steps.test-acl.outcome != 'skipped')
        run: |
          # Determine which operation was performed
          if [[ "${{ github.event_name }}" == "push" ]]; then
            OPERATION="deployment"
            STEP_OUTCOME="${{ steps.deploy-acl.outcome }}"
            STEP_OUTPUTS="${{ toJson(steps.deploy-acl.outputs) }}"
          else
            OPERATION="testing"
            STEP_OUTCOME="${{ steps.test-acl.outcome }}"
            STEP_OUTPUTS="${{ toJson(steps.test-acl.outputs) }}"
          fi

          echo "ACL_OPERATION=${OPERATION}" >> $GITHUB_ENV
          echo "ACL_OUTCOME=${STEP_OUTCOME}" >> $GITHUB_ENV
          echo "status=${STEP_OUTCOME}" >> $GITHUB_OUTPUT
          echo "operation=${OPERATION}" >> $GITHUB_OUTPUT

          # Create detailed GitHub job summary
          cat >> $GITHUB_STEP_SUMMARY << EOF
          ## 🔒 Tailscale ACL Operation Results

          **Operation**: ${OPERATION^}
          **Status**: $(if [[ "${STEP_OUTCOME}" == "success" ]]; then echo "✅ Success"; else echo "❌ Failed"; fi)
          **Policy File**: \`policy.hujson\`
          **Tailnet**: \`${{ secrets.TS_TAILNET }}\`
          **Authentication**: $(if [[ -n "${{ secrets.TS_OAUTH_CLIENT_ID }}" ]]; then echo "OAuth Client (Recommended)"; else echo "API Key (Legacy)"; fi)

          EOF

          if [[ "${STEP_OUTCOME}" == "success" ]]; then
            cat >> $GITHUB_STEP_SUMMARY << EOF
          ### ✅ Operation Completed Successfully

          The ACL policy has been successfully $(if [[ "${OPERATION}" == "deployment" ]]; then echo "deployed to production"; else echo "validated"; fi).

          EOF

            if [[ "${OPERATION}" == "testing" ]]; then
              cat >> $GITHUB_STEP_SUMMARY << EOF
          **Next Steps**:
          - Review the test results above
          - If everything looks correct, merge this PR to deploy to production
          - Monitor your Tailscale admin console for any connectivity issues

          EOF
            fi
            echo "SUCCESS: ACL ${OPERATION} completed successfully"
          else
            cat >> $GITHUB_STEP_SUMMARY << EOF
          ### ❌ Operation Failed

          The ACL ${OPERATION} encountered errors. Please review the details below and fix the issues.

          **Common ACL Validation Issues:**
          - **Syntax Errors**: Check for missing commas, brackets, or quotes in your policy.hujson
          - **Invalid References**: Ensure all tags, groups, and hosts referenced in rules are properly defined
          - **Permission Issues**: Verify that the authentication credentials have sufficient permissions
          - **Network Connectivity**: Check if the GitHub runner can reach Tailscale's API endpoints

          **Troubleshooting Steps:**
          1. 🔍 **Review the error logs** in the step output above
          2. 🧪 **Test locally** using \`tailscale configure acl test policy.hujson\`
          3. 📖 **Check documentation** at https://tailscale.com/kb/1018/acls/
          4. 🔑 **Verify credentials** in your repository secrets
          5. 🏷️ **Validate references** ensure all tags/groups exist before use

          **Security Recommendation:**
          $(if [[ -z "${{ secrets.TS_OAUTH_CLIENT_ID }}" ]]; then echo "Consider migrating from API keys to OAuth client authentication for better security. See: https://tailscale.com/kb/1215/oauth-clients/"; else echo "✅ Using OAuth client authentication (recommended)"; fi)

          EOF

            echo "::error::ACL ${OPERATION} failed with outcome: ${STEP_OUTCOME}"
            echo "ERROR: ACL ${OPERATION} failed"
          fi

      - name: "Handle ACL operation failure"
        if: steps.acl-result.outputs.status == 'failure'
        run: |
          echo "::error::ACL ${{ steps.acl-result.outputs.operation }} failed"
          echo "::notice::Check the job summary above for detailed troubleshooting guidance"
          exit 1

  # Network Topology Generation Job - Creates visualization maps
  network-topology:
    name: "Generate Network Topology"
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [validate-environment, test-suite]
    if: always() && needs.validate-environment.outputs.should-run-topology == 'true' && needs.test-suite.result == 'success'

    permissions:
      contents: write  # Required for committing generated files

    outputs:
      topology-generation-status: ${{ steps.generate-topology.outputs.status }}
      topology-file-generated: ${{ steps.validate-output.outputs.file-exists }}

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
        with:
          ref: ${{ github.head_ref || github.ref }}
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: "Set up Python with comprehensive caching"
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065  # v5.6.0
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
          cache-dependency-path: 'requirements.txt'

      - name: "Install dependencies with caching"
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: "Validate input files"
        run: |
          if [[ ! -f "policy.hujson" ]]; then
            echo "ERROR: policy.hujson file not found"
            exit 1
          fi

          if [[ ! -f "main.py" ]]; then
            echo "ERROR: main.py file not found"
            exit 1
          fi

          echo "SUCCESS: Input file validation passed"

      - name: "Generate network topology map"
        id: generate-topology
        run: |
          echo "INFO: Starting network topology generation..."

          # Run with error handling
          if python main.py; then
            echo "SUCCESS: Network topology generation completed successfully"
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "ERROR: Network topology generation failed"
            echo "status=failure" >> $GITHUB_OUTPUT
            exit 1
          fi
        continue-on-error: false

      - name: "Validate generated files"
        id: validate-output
        run: |
          if [[ ! -f "network_topology.html" ]]; then
            echo "ERROR: Expected output file network_topology.html was not generated"
            echo "file-exists=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Basic HTML validation
          if ! grep -q "<html" network_topology.html; then
            echo "ERROR: Generated file does not appear to be valid HTML"
            echo "file-exists=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          file_size=$(stat -c%s network_topology.html)
          if [[ $file_size -lt 1000 ]]; then
            echo "ERROR: Generated file is suspiciously small ($file_size bytes)"
            echo "file-exists=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          echo "SUCCESS: Generated file validation passed (size: $file_size bytes)"
          echo "file-exists=true" >> $GITHUB_OUTPUT

      - name: "Create topology generation summary"
        run: |
          echo "## Network Topology Generation Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **Status**: SUCCESS" >> $GITHUB_STEP_SUMMARY
          echo "- **Generated File**: network_topology.html" >> $GITHUB_STEP_SUMMARY
          echo "- **File Size**: $(stat -c%s network_topology.html) bytes" >> $GITHUB_STEP_SUMMARY
          echo "- **Generation Time**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY

      - name: "Upload topology artifact"
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02  # v4.6.2
        with:
          name: network-topology-${{ github.sha }}
          path: network_topology.html
          retention-days: 7

  # Conditional Commit Job - Only commits if ACL operations succeeded
  commit-topology:
    name: "Commit Network Topology"
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [validate-environment, acl-management, network-topology]
    if: |
      always() &&
      needs.network-topology.result == 'success' &&
      needs.network-topology.outputs.topology-file-generated == 'true' &&
      github.event_name == 'push' &&
      (
        (needs.validate-environment.outputs.should-run-acl == 'false') ||
        (needs.acl-management.result == 'success' && needs.acl-management.outputs.acl-operation-status == 'success')
      )

    permissions:
      contents: write  # Required for committing files

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
        with:
          ref: ${{ github.head_ref || github.ref }}
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: "Download topology artifact"
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093  # v4.3.0
        with:
          name: network-topology-${{ github.sha }}
          path: .

      - name: "Verify downloaded file"
        run: |
          echo "INFO: Preparing to commit network topology file"
          echo "ACL Management Status: ${{ needs.acl-management.outputs.acl-operation-status }}"
          echo "Topology Generation Status: ${{ needs.network-topology.outputs.topology-generation-status }}"

          if [[ ! -f "network_topology.html" ]]; then
            echo "ERROR: network_topology.html not found after artifact download"
            exit 1
          fi

          echo "SUCCESS: network_topology.html found and ready for commit"

      - name: "Commit updated network topology"
        uses: stefanzweifel/git-auto-commit-action@778341af668090896ca464160c2def5d1d1a3eb0  # v6.0.1
        with:
          commit_message: |
            Auto-update network topology visualization

            - Generated from commit: ${{ github.sha }}
            - Workflow run: ${{ github.run_id }}
            - Generated at: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
          file_pattern: "network_topology.html"
          commit_user_name: "github-actions[bot]"
          commit_user_email: "github-actions[bot]@users.noreply.github.com"
          commit_author: "GitHub Actions <github-actions[bot]@users.noreply.github.com>"
          skip_dirty_check: false
          disable_globbing: false

  # Notification and Summary Job - Provides comprehensive workflow results
  workflow-summary:
    name: "Workflow Summary & Notifications"
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [validate-environment, test-suite, acl-management, network-topology, commit-topology]
    if: always()

    steps:
      - name: "Generate comprehensive workflow summary"
        run: |
          echo "# Tailscale Workflow Execution Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Workflow**: ${{ github.workflow }}" >> $GITHUB_STEP_SUMMARY
          echo "**Trigger**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Run ID**: ${{ github.run_id }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "## Job Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Environment validation
          if [[ "${{ needs.validate-environment.result }}" == "success" ]]; then
            echo "- SUCCESS **Environment Validation**: Passed" >> $GITHUB_STEP_SUMMARY
          else
            echo "- ERROR **Environment Validation**: Failed" >> $GITHUB_STEP_SUMMARY
          fi

          # Test suite
          if [[ "${{ needs.test-suite.result }}" == "success" ]]; then
            echo "- SUCCESS **Test Suite**: All tests passed" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.test-suite.result }}" == "skipped" ]]; then
            echo "- SKIPPED **Test Suite**: Skipped (no changes detected)" >> $GITHUB_STEP_SUMMARY
          else
            echo "- ERROR **Test Suite**: Tests failed" >> $GITHUB_STEP_SUMMARY
          fi

          # ACL management
          if [[ "${{ needs.acl-management.result }}" == "success" ]]; then
            echo "- SUCCESS **ACL Management**: ${{ needs.acl-management.outputs.acl-operation-details }}" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.acl-management.result }}" == "skipped" ]]; then
            echo "- SKIPPED **ACL Management**: Skipped (no ACL changes detected)" >> $GITHUB_STEP_SUMMARY
          else
            echo "- ERROR **ACL Management**: Failed" >> $GITHUB_STEP_SUMMARY
          fi

          # Network topology
          if [[ "${{ needs.network-topology.result }}" == "success" ]]; then
            echo "- SUCCESS **Network Topology**: Generated successfully" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.network-topology.result }}" == "skipped" ]]; then
            echo "- SKIPPED **Network Topology**: Skipped (no changes detected)" >> $GITHUB_STEP_SUMMARY
          else
            echo "- ERROR **Network Topology**: Generation failed" >> $GITHUB_STEP_SUMMARY
          fi

          # Commit topology
          if [[ "${{ needs.commit-topology.result }}" == "success" ]]; then
            echo "- SUCCESS **Commit Topology**: File committed successfully" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.commit-topology.result }}" == "skipped" ]]; then
            echo "- SKIPPED **Commit Topology**: Skipped (ACL operation failed or not push event)" >> $GITHUB_STEP_SUMMARY
          else
            echo "- ERROR **Commit Topology**: Commit failed" >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## Useful Links" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- [View Network Topology](./network_topology.html)" >> $GITHUB_STEP_SUMMARY
          echo "- [Workflow Run](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
          echo "- [Commit Details](${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }})" >> $GITHUB_STEP_SUMMARY

      - name: "Handle workflow failures"
        if: contains(needs.*.result, 'failure')
        run: |
          echo "::error::One or more workflow jobs failed"
          echo "::notice::Check the job logs above for detailed error information"

          # Create failure summary
          echo "## Workflow Failure Details" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [[ "${{ needs.validate-environment.result }}" == "failure" ]]; then
            echo "- **Environment Validation**: Failed - Check secrets and repository configuration" >> $GITHUB_STEP_SUMMARY
          fi

          if [[ "${{ needs.test-suite.result }}" == "failure" ]]; then
            echo "- **Test Suite**: Failed - Review test failures and fix code issues" >> $GITHUB_STEP_SUMMARY
          fi

          if [[ "${{ needs.acl-management.result }}" == "failure" ]]; then
            echo "- **ACL Management**: Failed - Check ACL syntax and Tailscale API connectivity" >> $GITHUB_STEP_SUMMARY
          fi

          if [[ "${{ needs.network-topology.result }}" == "failure" ]]; then
            echo "- **Network Topology**: Failed - Check Python dependencies and script execution" >> $GITHUB_STEP_SUMMARY
          fi

          if [[ "${{ needs.commit-topology.result }}" == "failure" ]]; then
            echo "- **Commit Topology**: Failed - Check repository permissions and file generation" >> $GITHUB_STEP_SUMMARY
          fi

          exit 1

      - name: "Workflow completion notification"
        if: success()
        run: |
          echo "SUCCESS: Workflow completed successfully!"
          echo "All jobs executed as expected and completed without errors."
